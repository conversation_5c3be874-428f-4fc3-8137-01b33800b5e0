/*
 * Qt-based reimplementation of KiCad PAD class
 * 
 * This class represents a PCB pad (footprint pin) using Qt frameworks.
 * It provides comprehensive pad shape management, layer configuration,
 * drilling support, thermal management, and custom shape capabilities.
 */

#pragma once

#include "eda_board_connected_object.h"
#include "eda_padstack_data.h"
#include <QtCore/QString>

// Type alias for chamfer positions (from eda_padstack_data.h)
using QtChamferPositions = int;
#include <QtCore/QVector>
#include <QtCore/QPointF>
#include <QtCore/QSizeF>
#include <QtCore/QRectF>
#include <QtCore/QSet>
#include <QtCore/QMap>
#include <QtCore/QList>

#include <QtCore/QMetaEnum>
#include <QtCore/QMutex>
#include <QtCore/QHash>
#include <QtCore/QUuid>
#include <QtCore/QLoggingCategory>
#include <QtCore/QVariantMap>
#include <functional>
#include <memory>


// Forward declarations for dependencies not in migration scope
class EDA_FOOTPRINT_DATA;
class QtBoardDesignSettings;
class QtShape;
class QtShapeSegment;
class QtShapePolySet;
class QtShapeCompound;
class EDA_BRD_SHAPE_DATA;
class EDA_ZONE_DATA;
class QtProgressReporter;

/**
 * @brief Qt-based pad attribute enumeration
 */
enum class QtPadAttribute : int {
    PTH = 0,        ///< Plated through hole pad
    SMD = 1,        ///< SMD pad on solder paste layer
    Connector = 2,   ///< Like SMD, no solder paste
    NPTH = 3        ///< Non-plated through hole
};

/**
 * @brief Qt-based pad property enumeration for fabrication
 */
enum class QtPadProperty : int {
    None = 0,
    BGA = 1,
    FiducialGlobal = 2,
    FiducialLocal = 3,
    TestPoint = 4,
    HeatSink = 5,
    Castellated = 6,
    Mechanical = 7
};


/**
 * @brief Qt-based reimplementation of KiCad's PAD class
 * 
 * This class represents a PCB pad (footprint pin) with comprehensive
 * shape management, layer configuration, drilling support, and
 * thermal management using Qt frameworks.
 */
class EDA_PAD_DATA : public EDA_BOARD_CONNECTED_OBJECT
{

public:
    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_PAD_DATA(EDA_FOOTPRINT_DATA* parent = nullptr);
    EDA_PAD_DATA(const EDA_PAD_DATA& other);
    ~EDA_PAD_DATA() override;

    // Assignment operator
    EDA_PAD_DATA& operator=(const EDA_PAD_DATA& other);

    //==========================================================================
    // TYPE IDENTIFICATION
    //==========================================================================
    static bool classOf(const EDA_OBJECT_DATA* item);
    QString getClassName() const override { return QStringLiteral("EDA_PAD_DATA"); }
    QString getClass() const override { return QStringLiteral("PAD"); }

    //==========================================================================
    // BASIC PROPERTIES
    //==========================================================================
    
    QString getNumber() const { return m_number; }
    /**
     * @brief Set the pad number/name
     * @param number New pad number/identifier
     */
    void setNumber(const QString& number);
    
    /**
     * @brief Check if this pad type can have a number
     * @return true if pad can have a number
     */
    bool canHaveNumber() const;
    
    QString getPinFunction() const { return m_pinFunction; }
    /**
     * @brief Set the electrical function of this pin
     * @param function Pin function description
     */
    void setPinFunction(const QString& function);
    
    QString getPinType() const { return m_pinType; }
    /**
     * @brief Set the electrical type of this pin
     * @param type Pin type (input/output/bidirectional/etc)
     */
    void setPinType(const QString& type);
    
    QtPadAttribute getAttribute() const { return m_attribute; }
    /**
     * @brief Set pad mounting attribute (PTH/SMD/NPTH/Connector)
     * @param attribute New pad attribute
     */
    void setAttribute(QtPadAttribute attribute);
    
    QtPadProperty getProperty() const { return m_property; }
    /**
     * @brief Set pad fabrication property (BGA/Fiducial/TestPoint/etc)
     * @param property New pad property
     */
    void setProperty(QtPadProperty property);
    
    //==========================================================================
    // POSITION AND ORIENTATION
    //==========================================================================
    QPointF getPosition() const override { return m_position; }
    void setPosition(const QPointF& position) override;
    
    QPointF getCenter() const override { return getPosition(); }
    
    double getOrientation() const { return m_padStack.orientation(); }
    void setOrientation(double degrees);
    
    double getFPRelativeOrientation() const;
    void setFPRelativeOrientation(double degrees);
    
    //==========================================================================
    // PADSTACK MANAGEMENT
    //==========================================================================
    const EDA_PADSTACK_DATA& padStack() const { return m_padStack; }
    EDA_PADSTACK_DATA& padStack() { return m_padStack; }
    
    /**
     * @brief Replace this pad's padstack with a new one
     * @param padStack New padstack configuration
     */
    void setPadStack(const EDA_PADSTACK_DATA& padStack);
    
    //==========================================================================
    // SHAPE PROPERTIES
    //==========================================================================
    
    // Basic shape properties (convenience methods for normal padstack mode)
    QtPadShape getShape(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    void setShape(QtPadShape shape, QtPcbLayerId layer = QtPcbLayerId::FCu);
    
    QSizeF getSize(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    void setSize(const QSizeF& size, QtPcbLayerId layer = QtPcbLayerId::FCu);
    
    QPointF getOffset(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    void setOffset(const QPointF& offset, QtPcbLayerId layer = QtPcbLayerId::FCu);
    
    // Shape position on board
    QPointF shapePosition(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    
    //==========================================================================
    // DRILL PROPERTIES
    //==========================================================================
    bool hasHole() const override;
    bool isDrilledHole() const;
    
    QSizeF getDrillSize() const { return m_padStack.drill().size; }
    void setDrillSize(const QSizeF& size);
    
    QtPadDrillShape getDrillShape() const { return m_padStack.drill().shape; }
    void setDrillShape(QtPadDrillShape shape);
    
    //==========================================================================
    // LAYER MANAGEMENT
    //==========================================================================
    QtLayerSet getLayerSet() const override { return m_padStack.layerSet(); }
    void setLayerSet(const QtLayerSet& layers) override;
    
    bool isOnCopperLayer() const override;
    QtPcbLayerId getPrincipalLayer() const;
    
    //==========================================================================
    // PARENT AND RELATIONSHIPS
    //==========================================================================
    EDA_FOOTPRINT_DATA* getParentFootprint() const;
    
    // Logical pad relationships
    bool sameLogicalPadAs(const EDA_PAD_DATA* other) const;
    bool sharesNetTieGroup(const EDA_PAD_DATA* other) const;
    bool isNoConnectPad() const;
    bool isFreePad() const;
    bool isFlipped() const;
    
    //==========================================================================
    // GEOMETRIC TRANSFORMATIONS
    //==========================================================================
    void move(const QPointF& moveVector) override;
    void rotate(const QPointF& rotationCenter, double angleRadians) override;
    void flip(const QPointF& center, QtFlipDirection flipDirection) override;
    
    //==========================================================================
    // ROUNDED RECTANGLE PROPERTIES
    //==========================================================================
    double getRoundRectCornerRadius(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    void setRoundRectCornerRadius(QtPcbLayerId layer, double radius);
    
    double getRoundRectRadiusRatio(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    void setRoundRectRadiusRatio(QtPcbLayerId layer, double ratio);
    
    //==========================================================================
    // CHAMFERED RECTANGLE PROPERTIES
    //==========================================================================
    double getChamferRatio(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    void setChamferRatio(QtPcbLayerId layer, double ratio);
    
    QtChamferPositions getChamferPositions(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    void setChamferPositions(QtPcbLayerId layer, QtChamferPositions positions);
    
    //==========================================================================
    // TRAPEZOID PROPERTIES
    //==========================================================================
    QSizeF getTrapezoidDelta(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    void setTrapezoidDelta(QtPcbLayerId layer, const QSizeF& delta);
    
    //==========================================================================
    // CUSTOM SHAPE SUPPORT
    //==========================================================================
    QtPadShape getAnchorPadShape(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    void setAnchorPadShape(QtPcbLayerId layer, QtPadShape shape);
    
    QtCustomShapeZoneMode getCustomShapeZoneMode() const;
    void setCustomShapeZoneMode(QtCustomShapeZoneMode mode);
    
    // Custom shape primitives
    QList<std::shared_ptr<EDA_BRD_SHAPE_DATA>> getPrimitives(QtPcbLayerId layer) const;
    void addPrimitive(QtPcbLayerId layer, std::shared_ptr<EDA_BRD_SHAPE_DATA> primitive);
    void replacePrimitives(QtPcbLayerId layer, const QList<std::shared_ptr<EDA_BRD_SHAPE_DATA>>& primitives);
    void appendPrimitives(QtPcbLayerId layer, const QList<std::shared_ptr<EDA_BRD_SHAPE_DATA>>& primitives);
    void deletePrimitivesList(QtPcbLayerId layer = QtPcbLayerId::UndefinedLayer);
    
    // Polygon merging for custom shapes
    void mergePrimitivesAsPolygon(QtPcbLayerId layer, QtShapePolySet* mergedPolygon,
                                  QtErrorLoc errorLocation = QtErrorLoc::Inside) const;
    
    //==========================================================================
    // CLEARANCE AND MARGINS
    //==========================================================================
    std::optional<int> getLocalClearance() const;
    void setLocalClearance(std::optional<int> clearance);
    
    std::optional<int> getLocalSolderMaskMargin() const;
    void setLocalSolderMaskMargin(std::optional<int> margin);
    
    std::optional<int> getLocalSolderPasteMargin() const;
    void setLocalSolderPasteMargin(std::optional<int> margin);
    
    std::optional<double> getLocalSolderPasteMarginRatio() const;
    void setLocalSolderPasteMarginRatio(std::optional<double> ratio);
    
    int getSolderMaskExpansion(QtPcbLayerId layer) const;
    QSizeF getSolderPasteMargin(QtPcbLayerId layer) const;
    
    //==========================================================================
    // ZONE CONNECTION AND THERMAL MANAGEMENT
    //==========================================================================
    QtZoneConnection getLocalZoneConnection() const;
    void setLocalZoneConnection(QtZoneConnection connection);
    
    // Thermal spoke properties
    std::optional<int> getLocalThermalSpokeWidth() const;
    void setLocalThermalSpokeWidth(std::optional<int> width);
    
    double getThermalSpokeAngle() const;
    void setThermalSpokeAngle(double degrees);
    
    std::optional<int> getLocalThermalGap() const;
    void setLocalThermalGap(std::optional<int> gap);
    
    //==========================================================================
    // PAD TO DIE LENGTH
    //==========================================================================
    int getPadToDieLength() const { return m_padToDieLength; }
    void setPadToDieLength(int length);
    
    //==========================================================================
    // SHAPE GENERATION AND GEOMETRY
    //==========================================================================
    std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId layer = QtPcbLayerId::UndefinedLayer,
                                              QtFlashing flash = QtFlashing::Default) const override;
    std::shared_ptr<QtShapeSegment> getEffectiveHoleShape() const override;
    
    int getBoundingRadius() const;
    
    //==========================================================================
    // POLYGON TRANSFORMATION
    //==========================================================================
    void transformShapeToPolygon(QtShapePolySet& buffer, QtPcbLayerId layer,
                                double clearance, double error, 
                                QtErrorLoc errorLocation = QtErrorLoc::Outside,
                                bool ignoreLineWidth = false) const override;
    
    bool transformHoleToPolygon(QtShapePolySet& buffer, double clearance, double error,
                               QtErrorLoc errorLocation = QtErrorLoc::Inside) const;
    
    //==========================================================================
    // APERTURE SUPPORT
    //==========================================================================
    bool isAperturePad() const;
    
    //==========================================================================
    // LAYER FLASHING
    //==========================================================================
    bool flashLayer(QtPcbLayerId layer, bool onlyCheckIfPermitted = false) const;
    bool canFlashLayer(QtPcbLayerId layer) const { return flashLayer(layer, true); }
    bool flashLayer(const QtLayerSet& layers) const;
    bool conditionallyFlashed(QtPcbLayerId layer) const;
    
    //==========================================================================
    // UNCONNECTED LAYER HANDLING
    //==========================================================================
    QtUnconnectedLayerMode getUnconnectedLayerMode() const;
    void setUnconnectedLayerMode(QtUnconnectedLayerMode mode);
    
    //==========================================================================
    // HIT TESTING
    //==========================================================================
    bool hitTest(const QPointF& position, double accuracy = 0.0) const override;
    bool hitTest(const QRectF& rect, bool contained = false, double accuracy = 0.0) const override;
    
    //==========================================================================
    // BOUNDING BOX AND VISUALIZATION
    //==========================================================================
    QRectF getBoundingBox() const override;
    
    //==========================================================================
    // SETTINGS IMPORT
    //==========================================================================
    void importSettingsFrom(const EDA_PAD_DATA& masterPad);
    
    //==========================================================================
    // DUPLICATION AND COMPARISON
    //==========================================================================
    EDA_BOARD_OBJECT_DATA* duplicate() const override;
    double similarity(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_PAD_DATA& other) const;
    
    //==========================================================================
    // DIRTY STATE MANAGEMENT
    //==========================================================================
    bool isDirty() const { return m_shapesDirty || m_polyDirty; }
    void setDirty();
    void buildEffectiveShapes() const;
    void buildEffectivePolygon() const;
    
    //==========================================================================
    // QT SERIALIZATION AND DESCRIPTION
    //==========================================================================
    QVariantMap toVariantMap() const override;
    void fromVariantMap(const QVariantMap& map) override;
    
    QString getItemDescription(bool full = false) const;
    QString toString() const override;
    
    //==========================================================================
    // STATIC UTILITIES
    //==========================================================================
    
    // Static layer masks for different pad types
    static QtLayerSet PTHMask();
    static QtLayerSet SMDMask();
    static QtLayerSet ConnectorSMDMask();
    static QtLayerSet UnplatedHoleMask();
    static QtLayerSet ApertureMask();
    
    // Pad comparison
    static int comparePads(const EDA_PAD_DATA* padRef, const EDA_PAD_DATA* padCmp);
    
    // Shape name for UI
    QString getShapeName(QtPcbLayerId layer = QtPcbLayerId::FCu) const;
    QString getAttributeName() const;
    

protected:
    //==========================================================================
    // PROTECTED HELPER METHODS
    //==========================================================================
    void swapData(EDA_BOARD_OBJECT_DATA* other) override;
    void clearCaches() const;
    void notifyGeometryChange();
    
    // Shape building helpers
    std::shared_ptr<QtShapeCompound> buildEffectiveShapeForLayer(QtPcbLayerId layer) const;
    void addPrimitivesToPolygon(QtPcbLayerId layer, QtShapePolySet* mergedPolygon, 
                               double error, QtErrorLoc errorLocation) const;

private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    
    // Basic properties
    QString m_number;           ///< Pad number (pin number in schematic)
    QString m_pinFunction;      ///< Pin function name in schematic
    QString m_pinType;          ///< Pin electrical type in schematic
    QPointF m_position;         ///< Pad position on board
    
    // Pad attributes
    QtPadAttribute m_attribute = QtPadAttribute::PTH;
    QtPadProperty m_property = QtPadProperty::None;
    int m_padToDieLength = 0;   ///< Length from pad to die inside package
    
    // Padstack containing all layer-specific properties
    EDA_PADSTACK_DATA m_padStack;
    
    // Shape caching for performance
    mutable bool m_shapesDirty = true;
    mutable bool m_polyDirty = true;
    mutable QMutex m_shapesBuildingLock;
    mutable QMutex m_polyBuildingLock;
    
    // Subnet for ratsnest computations
    int m_subRatsnest = 0;
    
    // Zone layer overrides
    QMap<QtPcbLayerId, QtZoneLayerOverride> m_zoneLayerOverrides;
    
    // Cached effective shapes per layer
    mutable QHash<QtPcbLayerId, std::shared_ptr<QtShapeCompound>> m_effectiveShapes;
    mutable std::shared_ptr<QtShapeSegment> m_effectiveHoleShape;
    mutable QHash<QtPcbLayerId, std::shared_ptr<QtShapePolySet>> m_effectivePolygons;
    mutable QRectF m_effectiveBoundingBox;
    mutable int m_effectiveBoundingRadius = 0;
};

//=============================================================================
// UTILITY FUNCTIONS AND TYPE ALIASES
//=============================================================================

// Container type aliases
using QtPads = QList<EDA_PAD_DATA*>;
using QtPadList = QVector<EDA_PAD_DATA*>;
using QtPadSet = QSet<EDA_PAD_DATA*>;

//// Hash support for pad enums
//inline uint qHash(QtPadAttribute attr, uint seed = 0) {
//    return qHash(static_cast<int>(attr), seed);
//}

//inline uint qHash(QtPadProperty prop, uint seed = 0) {
//    return qHash(static_cast<int>(prop), seed);
//}

// Utility namespace for pad operations
namespace QtPadUtils {
    /**
     * @brief Get canonical name for pad shape
     */
    QString getShapeCanonicalName(QtPadShape shape);
    
    /**
     * @brief Get pad shape from canonical name
     */
    QtPadShape getShapeFromCanonicalName(const QString& name);
    
    /**
     * @brief Get canonical name for pad attribute
     */
    QString getAttributeCanonicalName(QtPadAttribute attribute);
    
    /**
     * @brief Get pad attribute from canonical name
     */
    QtPadAttribute getAttributeFromCanonicalName(const QString& name);
    
    /**
     * @brief Validate pad configuration
     */
    bool validatePadConfiguration(const EDA_PAD_DATA* pad, QString* error);
    
    /**
     * @brief Calculate pad area for given layer
     */
    double calculatePadArea(const EDA_PAD_DATA* pad, QtPcbLayerId layer);
    
    /**
     * @brief Check if two pads can be merged
     */
    bool canMergePads(const EDA_PAD_DATA* pad1, const EDA_PAD_DATA* pad2);
}

