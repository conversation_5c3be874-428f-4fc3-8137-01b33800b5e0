/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright The KiCad Developers, see AUTHORS.txt for contributors.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, you may find one here:
 * http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
 * or you may search the http://www.gnu.org website for the version 2 license,
 * or you may write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
 */

#include "eda_brd_field_data.h"
#include "eda_footprint_data.h"
#include <QRegularExpression>
#include <QDebug>

// Level of Detail constants
constexpr int LOD_SHOW = 0;
constexpr int LOD_HIDE = 100;

// Forward declaration for Google protobuf
namespace google {
namespace protobuf {
class Any {};
}
}

// Temporary implementations for dependencies
namespace kiapi {
namespace board {
namespace types {
    
class Field {
public:
    void set_name(const std::string& name) { m_name = name; }
    std::string name() const { return m_name; }
    void set_visible(bool visible) { m_visible = visible; }
    bool visible() const { return m_visible; }
    bool has_id() const { return m_hasId; }
    bool has_text() const { return m_hasText; }
    
    struct Id {
        void set_id(int id) { m_id = id; }
        int id() const { return m_id; }
        int m_id;
    };
    
    Id* mutable_id() { 
        m_hasId = true;
        return &m_idField; 
    }
    const Id& id() const { return m_idField; }
    
    struct Text {
        int layer() const { return m_layer; }
        void set_layer(int layer) { m_layer = layer; }
        int m_layer = 0;
    };
    
    Text* mutable_text() { 
        m_hasText = true;
        return &m_textField; 
    }
    const Text& text() const { return m_textField; }
    
private:
    std::string m_name;
    bool m_visible = true;
    bool m_hasId = false;
    bool m_hasText = false;
    Id m_idField;
    Text m_textField;
};

enum BoardLayer {
    BL_UNKNOWN = 0,
    F_SilkS = 1
};

}  // namespace types
}  // namespace board
}  // namespace kiapi

// Temporary KIGFX namespace implementations
namespace KIGFX {

class VIEW {
public:
    bool isLayerVisible(int layer) const { return true; }
    void* getPainter() const { return nullptr; }
};

class PCB_PAINTER {
public:
    struct PCB_RENDER_SETTINGS {
        bool m_ForceShowFieldsWhenFPSelected = false;
    };
    
    PCB_RENDER_SETTINGS* GetSettings() { return &m_settings; }
    
private:
    PCB_RENDER_SETTINGS m_settings;
};

}  // namespace KIGFX

// Use QtPcbLayerId from qt_temporary_implementations.h
// F_SilkS is already defined there as constexpr QtPcbLayerId F_SilkS = QtPcbLayerId::FSilkS;

// Helper function implementations
QString EDA_BRD_FIELD_DATA::getCanonicalFieldName(int idx)
{
    switch(idx) {
        case REFERENCE_FIELD: return QStringLiteral("Reference");
        case VALUE_FIELD: return QStringLiteral("Value");
        case FOOTPRINT_FIELD: return QStringLiteral("Footprint");
        case DATASHEET_FIELD: return QStringLiteral("Datasheet");
        case DESCRIPTION_FIELD: return QStringLiteral("Description");
        default: return QString();
    }
}

QString EDA_BRD_FIELD_DATA::getUserFieldName(int aFieldNdx, bool aTranslate)
{
    if (aTranslate) {
        return QStringLiteral("User Field %1").arg(aFieldNdx);
    } else {
        return QStringLiteral("User_Field_%1").arg(aFieldNdx);
    }
}

// Check if a string is a URL
static bool isURL(const QString& text)
{
    static QRegularExpression urlRegex(
        QStringLiteral("^(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]")
    );
    return urlRegex.match(text).hasMatch();
}

// Constructor implementations
EDA_BRD_FIELD_DATA::EDA_BRD_FIELD_DATA(EDA_FOOTPRINT_DATA* aParent, int aFieldId, const QString& aName)
    : EDA_BRD_TEXT_DATA(aParent, QtKicadType::PcbFieldT)
    , m_id(aFieldId)
    , m_name(aName)
{
}

EDA_BRD_FIELD_DATA::EDA_BRD_FIELD_DATA(const EDA_BRD_TEXT_DATA& aText, int aFieldId, const QString& aName)
    : EDA_BRD_TEXT_DATA(aText.getParent(), QtKicadType::PcbFieldT)
    , m_id(aFieldId)
    , m_name(aName)
{
    // Type is set in constructor call above
}

// Static methods
bool EDA_BRD_FIELD_DATA::classOf(const EDA_OBJECT_DATA* aItem)
{
    return aItem && aItem->getType() == QtKicadType::PcbFieldT;
}

// Type checking
bool EDA_BRD_FIELD_DATA::isType(const QVector<QtKicadType>& aScanTypes) const
{
    if (EDA_BOARD_OBJECT_DATA::isType(aScanTypes)) {
        return true;
    }
    
    for (QtKicadType scanType : aScanTypes) {
        if (scanType == QtKicadType::PcbFieldT) {
            return true;
        }
        // TODO: Add specific field type checking if needed
        // These constants need to be mapped to QtKicadType equivalents
    }
    
    return false;
}

// Serialization
void EDA_BRD_FIELD_DATA::serialize(google::protobuf::Any& aContainer) const
{
    kiapi::board::types::Field field;
    
    // Serialize base text properties
    QJsonObject textJson = EDA_BRD_TEXT_DATA::serialize();
    // In real implementation, would convert to protobuf and pack into field.mutable_text()
    
    field.set_name(getCanonicalName().toStdString());
    field.mutable_id()->set_id(getId());
    field.set_visible(isVisible());
    
    // In real implementation, would pack field into aContainer
}

bool EDA_BRD_FIELD_DATA::deserialize(const google::protobuf::Any& aContainer)
{
    kiapi::board::types::Field field;
    
    // In real implementation, would unpack from aContainer
    
    if (field.has_id()) {
        setIdInternal(field.id().id());
    }
    
    // Mandatory fields have a blank Name in the KiCad object
    if (!isMandatory()) {
        setName(QString::fromUtf8(field.name().c_str()));
    }
    
    if (field.has_text()) {
        QJsonObject textJson;
        // In real implementation, would convert from protobuf field.text() to QJsonObject
        EDA_BRD_TEXT_DATA::deserialize(textJson);
    }
    
    setVisible(field.visible());
    
    if (field.text().layer() == kiapi::board::types::BL_UNKNOWN) {
        setLayer(QtPcbLayerId::FSilkS);
    }
    
    return true;
}

// Field name management
QString EDA_BRD_FIELD_DATA::getName(bool aUseDefaultName) const
{
    if (getParent() && getParent()->getType() == QtKicadType::PcbFootprint) {
        if (isMandatory()) {
            return getCanonicalFieldName(m_id);
        } else if (m_name.isEmpty() && aUseDefaultName) {
            return getUserFieldName(m_id, false);
        } else {
            return m_name;
        }
    } else {
        qWarning() << "Unhandled field owner type.";
        return m_name;
    }
}

QString EDA_BRD_FIELD_DATA::getCanonicalName() const
{
    if (getParent() && getParent()->getType() == QtKicadType::PcbFootprint) {
        if (isMandatory()) {
            return getCanonicalFieldName(m_id);
        } else {
            return m_name;
        }
    } else {
        if (getParent()) {
            qWarning() << QString("Unhandled field owner type (id %1, parent type %2).")
                          .arg(m_id)
                          .arg(static_cast<int>(getParent()->getType()));
        }
        return m_name;
    }
}

void EDA_BRD_FIELD_DATA::setName(const QString& aName)
{
    if (m_name != aName) {
        m_name = aName;
    }
}

// Field type queries
bool EDA_BRD_FIELD_DATA::isMandatory() const
{
    return m_id == REFERENCE_FIELD
        || m_id == VALUE_FIELD
        || m_id == DATASHEET_FIELD
        || m_id == DESCRIPTION_FIELD;
}

bool EDA_BRD_FIELD_DATA::isHypertext() const
{
    return isURL(getShownText(false));
}

// Text descriptions
QString EDA_BRD_FIELD_DATA::getTextTypeDescription() const
{
    if (isMandatory()) {
        return getCanonicalFieldName(m_id);
    } else {
        return QStringLiteral("User Field");
    }
}

QString EDA_BRD_FIELD_DATA::getItemDescription(UNITS_PROVIDER* aUnitsProvider, bool aFull) const
{
    QString content = aFull ? getShownText(false) : getText(); // Simplified ellipsize
    
    EDA_FOOTPRINT_DATA* fp = dynamic_cast<EDA_FOOTPRINT_DATA*>(getParentFootprint());
    QString ref = fp ? fp->getReference() : QStringLiteral("Unknown");
    
    switch (m_id) {
        case REFERENCE_FIELD:
            return QString("Reference field of %1").arg(ref);
            
        case VALUE_FIELD:
            return QString("Value field of %1 (%2)").arg(ref).arg(content);
            
        case FOOTPRINT_FIELD:
            return QString("Footprint field of %1 (%2)").arg(ref).arg(content);
            
        case DATASHEET_FIELD:
            return QString("Datasheet field of %1 (%2)").arg(ref).arg(content);
            
        default:
            if (getName().isEmpty()) {
                return QString("Field of %1 (%2)").arg(ref).arg(content);
            } else {
                return QString("%1 field of %2 (%3)").arg(getName()).arg(ref).arg(content);
            }
    }
}

// View management
double EDA_BRD_FIELD_DATA::viewGetLOD(int aLayer, const QtView* aView) const
{
    if (!aView) {
        return LOD_SHOW;
    }
    
    // Qt implementation: simplified version without KIGFX dependencies
    // TODO: Add proper Qt render settings support when needed
    EDA_FOOTPRINT_DATA* fp = dynamic_cast<EDA_FOOTPRINT_DATA*>(getParentFootprint());
    if (fp && fp->isSelected()) {
        return LOD_SHOW; // Show field when footprint is selected
    }
    
    // Handle Render tab switches
    if (isValue() && !aView->isLayerVisible(LAYER_FP_VALUES)) {
        return LOD_HIDE;
    }
    
    if (isReference() && !aView->isLayerVisible(LAYER_FP_REFERENCES)) {
        return LOD_HIDE;
    }
    
    return EDA_BRD_TEXT_DATA::viewGetLOD(aLayer, aView);
}

// Cloning
EDA_OBJECT_DATA* EDA_BRD_FIELD_DATA::clone() const
{
    return new EDA_BRD_FIELD_DATA(*this);
}

// Data swapping
void EDA_BRD_FIELD_DATA::swapData(EDA_BOARD_OBJECT_DATA* aImage)
{
    EDA_BRD_FIELD_DATA* field = dynamic_cast<EDA_BRD_FIELD_DATA*>(aImage);
    if (!field) {
        qWarning() << "Attempted to swap data with non-field item";
        return;
    }
    
    // Swap all data
    std::swap(m_id, field->m_id);
    std::swap(m_name, field->m_name);
    
    // Swap base class data
    EDA_BRD_TEXT_DATA::swapData(aImage);
}

// Comparison operators
bool EDA_BRD_FIELD_DATA::operator==(const EDA_BOARD_OBJECT_DATA& aOther) const
{
    if (aOther.getType() != getType()) {
        return false;
    }
    
    const EDA_BRD_FIELD_DATA& other = static_cast<const EDA_BRD_FIELD_DATA&>(aOther);
    return *this == other;
}

bool EDA_BRD_FIELD_DATA::operator==(const EDA_BRD_FIELD_DATA& aOther) const
{
    return m_id == aOther.m_id 
        && m_name == aOther.m_name 
        && EDA_TEXT_DATA::operator==(aOther);
}

double EDA_BRD_FIELD_DATA::similarity(const EDA_BOARD_OBJECT_DATA& aOther) const
{
    if (getUuid() == aOther.getUuid()) {
        return 1.0;
    }
    
    if (aOther.getType() != getType()) {
        return 0.0;
    }
    
    const EDA_BRD_FIELD_DATA& other = static_cast<const EDA_BRD_FIELD_DATA&>(aOther);
    
    if (isMandatory() || other.isMandatory()) {
        if (m_id == other.m_id) {
            return 1.0;
        } else {
            return 0.0;
        }
    }
    
    if (m_name == other.m_name) {
        return 1.0;
    }
    
    return EDA_TEXT_DATA::similarity(other);
}