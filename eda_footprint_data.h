#ifndef QT_FOOTPRINT_H
#define QT_FOOTPRINT_H

#include <QString>
#include <QVector>
#include <QList>
#include <QMap>
#include <QSet>
#include <QPointF>
#include <QRectF>
#include <QMutex>
#include <QSharedPointer>

#include "eda_board_object_container.h"
#include "eda_pad_data.h"
#include "eda_zone_data.h"
#include "qt_temporary_implementations.h"

#include <optional>

// Forward declarations
class EDA_BOARD_DATA;
class EDA_BRD_FIELD_DATA;
class QtPcbGroup;
class EDA_BRD_TEXT_DATA;
class QtShape;
class QtGeneralCollector;
class QtEmbeddedFiles;
class QtComponentClass;
class QtNetInfo;
class QtReporter;
class QtUnitsProvider;
class QtView;
class QtOutlineErrorHandler;
class QtOutlineFont;

// Temporary enums until proper implementation

enum QtIncludeNpthT {
    DO_NOT_INCLUDE_NPTH = false,
    INCLUDE_NPTH = true
};

enum QtFootprintAttrT {
    FP_THROUGH_HOLE             = 0x0001,
    FP_SMD                      = 0x0002,
    FP_EXCLUDE_FROM_POS_FILES   = 0x0004,
    FP_EXCLUDE_FROM_BOM         = 0x0008,
    FP_BOARD_ONLY               = 0x0010,
    FP_JUST_ADDED               = 0x0020,
    FP_ALLOW_SOLDERMASK_BRIDGES = 0x0040,
    FP_ALLOW_MISSING_COURTYARD  = 0x0080,
    FP_DNP                      = 0x0100
};

// Footprint status flags
#define FP_is_LOCKED        0x01
#define FP_is_PLACED        0x02
#define FP_to_PLACE         0x04
#define FP_PADS_are_LOCKED  0x08

class QtFp3DModel {
public:
    QtFp3DModel() :
        m_scale(1.0, 1.0, 1.0),
        m_rotation(0.0, 0.0, 0.0),
        m_offset(0.0, 0.0, 0.0),
        m_opacity(1.0),
        m_show(true) {}

    QtVector3D m_scale;
    QtVector3D m_rotation;
    QtVector3D m_offset;
    double m_opacity;
    QString m_filename;
    bool m_show;

    bool operator==(const QtFp3DModel& other) const {
        return m_scale == other.m_scale
                && m_rotation == other.m_rotation
                && m_offset == other.m_offset
                && m_opacity == other.m_opacity
                && m_filename == other.m_filename
                && m_show == other.m_show;
    }
};

class EDA_FOOTPRINT_DATA : public EDA_BOARD_OBJECT_CONTAINER
{


public:
    EDA_FOOTPRINT_DATA(EDA_BOARD_DATA* parent = nullptr);
    EDA_FOOTPRINT_DATA(const EDA_FOOTPRINT_DATA& aFootprint);
    EDA_FOOTPRINT_DATA(EDA_FOOTPRINT_DATA&& aFootprint);
    virtual ~EDA_FOOTPRINT_DATA();

    EDA_FOOTPRINT_DATA& operator=(const EDA_FOOTPRINT_DATA& aOther);
    EDA_FOOTPRINT_DATA& operator=(EDA_FOOTPRINT_DATA&& aOther);

    static bool classOf(const EDA_BOARD_OBJECT_DATA* aItem);
    
    // Type identification
    QString getClassName() const override { return QStringLiteral("EDA_FOOTPRINT_DATA"); }
    QString getClass() const override { return QStringLiteral("FOOTPRINT"); }

    /**
     * @brief Set footprint position on board
     * @param aPos New position coordinates
     */
    void setPosition(const QPointF& aPos) override;
    QPointF getPosition() const override { return m_pos; }
    
    /**
     * @brief Set footprint orientation angle
     * @param aNewAngle New orientation angle
     */
    void setOrientation(const QtEdaAngle& aNewAngle);
    QtEdaAngle getOrientation() const { return m_orient; }
    
    void setOrientationDegrees(double aOrientation) {
        setOrientation(QtEdaAngle(aOrientation));
    }
    double getOrientationDegrees() const {
        return m_orient.value;
    }
    double getOrientationRadians() const {
        return m_orient.asRadians();
    }

    /**
     * @brief Set layer and flip footprint if needed
     * @param aLayer Target layer (FCu or BCu)
     */
    void setLayerAndFlip(QtPcbLayerId aLayer);
    QtPcbLayerId getLayer() const;
    QtPcbLayerId getSide() const;
    bool isFlipped() const;
    bool isOnLayer(QtPcbLayerId aLayer) const override;

    // Field management
    const QString& getReference() const;
    void setReference(const QString& aReference);
    QString getReferenceAsString() const { return getReference(); }
    
    const QString& getValue() const;
    void setValue(const QString& aValue);
    QString getValueAsString() const { return getValue(); }
    
    EDA_BRD_FIELD_DATA& reference();
    EDA_BRD_FIELD_DATA& value();
    const EDA_BRD_FIELD_DATA& reference() const;
    const EDA_BRD_FIELD_DATA& value() const;
    
    EDA_BRD_FIELD_DATA* getField(int aFieldType);
    const EDA_BRD_FIELD_DATA* getField(int aFieldType) const;
    EDA_BRD_FIELD_DATA* getFieldById(int aFieldId);
    EDA_BRD_FIELD_DATA* getFieldByName(const QString& aFieldName);
    bool hasFieldByName(const QString& aFieldName) const;
    QString getFieldText(const QString& aFieldName) const;
    
    void getFields(QVector<EDA_BRD_FIELD_DATA*>& aVector, bool aVisibleOnly) const;
    QVector<EDA_BRD_FIELD_DATA*> getFields(bool aVisibleOnly = false) const;
    void clearFields();
    EDA_BRD_FIELD_DATA* addField(const EDA_BRD_FIELD_DATA& aField);
    void removeField(const QString& aFieldName);
    int getNextFieldId() const;

    // Footprint ID and metadata
    const QtLibId& getFPID() const { return m_fpid; }
    void setFPID(const QtLibId& aFPID) { m_fpid = aFPID; }
    QString getFPIDAsString() const { return m_fpid.format(); }
    void setFPIDAsString(const QString& aFPID) { m_fpid.parse(aFPID); }
    
    QString getLibDescription() const { return m_libDescription; }
    void setLibDescription(const QString& aDesc) { m_libDescription = aDesc; }
    
    QString getKeywords() const { return m_keywords; }
    void setKeywords(const QString& aKeywords) { m_keywords = aKeywords; }
    
    const QtKiidPath& getPath() const { return m_path; }
    void setPath(const QtKiidPath& aPath) { m_path = aPath; }
    
    QString getSheetname() const { return m_sheetname; }
    void setSheetname(const QString& aSheetname) { m_sheetname = aSheetname; }
    
    QString getSheetfile() const { return m_sheetfile; }
    void setSheetfile(const QString& aSheetfile) { m_sheetfile = aSheetfile; }
    
    QString getFilters() const { return m_filters; }
    void setFilters(const QString& aFilters) { m_filters = aFilters; }

    // Collections
    QList<EDA_PAD_DATA*>& pads() { return m_pads; }
    const QList<EDA_PAD_DATA*>& pads() const { return m_pads; }
    
    QVector<EDA_BOARD_OBJECT_DATA*>& graphicalItems() { return m_drawings; }
    const QVector<EDA_BOARD_OBJECT_DATA*>& graphicalItems() const { return m_drawings; }
    
    QVector<EDA_ZONE_DATA*>& zones() { return m_zones; }
    const QVector<EDA_ZONE_DATA*>& zones() const { return m_zones; }
    
    QVector<QtPcbGroup*>& groups() { return m_groups; }
    const QVector<QtPcbGroup*>& groups() const { return m_groups; }
    
    QVector<QtFp3DModel>& models() { return m_3DDrawings; }
    const QVector<QtFp3DModel>& models() const { return m_3DDrawings; }

    // Private layers
    QtLayerSet getPrivateLayers() const { return m_privateLayers; }
    void setPrivateLayers(const QtLayerSet& aLayers) { m_privateLayers = aLayers; }

    // Container management
    void add(EDA_BOARD_OBJECT_DATA* aItem, QtAddMode aMode = QtAddMode::Insert, 
             bool aSkipConnectivity = false);
    void remove(EDA_BOARD_OBJECT_DATA* aItem, QtRemoveMode aMode = QtRemoveMode::Normal);

    // Net management
    void clearAllNets();
    bool fixUuids();

    // Attributes
    int getAttributes() const { return m_attributes; }
    void setAttributes(int aAttributes) { m_attributes = aAttributes; }
    
    bool isBoardOnly() const { return m_attributes & FP_BOARD_ONLY; }
    void setBoardOnly(bool aIsBoardOnly = true);
    
    bool isExcludedFromPosFiles() const { return m_attributes & FP_EXCLUDE_FROM_POS_FILES; }
    void setExcludedFromPosFiles(bool aExclude = true);
    
    bool isExcludedFromBOM() const { return m_attributes & FP_EXCLUDE_FROM_BOM; }
    void setExcludedFromBOM(bool aExclude = true);
    
    bool allowMissingCourtyard() const { return m_attributes & FP_ALLOW_MISSING_COURTYARD; }
    void setAllowMissingCourtyard(bool aAllow = true);
    
    bool isDNP() const { return m_attributes & FP_DNP; }
    void setDNP(bool aDNP = true);

    // Status flags
    bool isLocked() const override;
    void setLocked(bool isLocked) override;
    bool isConflicting() const;
    bool isPlaced() const;
    void setIsPlaced(bool isPlaced);
    bool needsPlaced() const;
    void setNeedsPlaced(bool needsPlaced);
    bool legacyPadsLocked() const;
    
    void setFlag(int aFlag) { m_arflag = aFlag; }
    void incrementFlag() { m_arflag += 1; }
    int getFlag() const { return m_arflag; }

    // Design rule overrides
    std::optional<int> getLocalClearance() const { return m_clearance; }
    void setLocalClearance(std::optional<int> aClearance) { m_clearance = aClearance; }
    
    std::optional<int> getLocalSolderMaskMargin() const { return m_solderMaskMargin; }
    void setLocalSolderMaskMargin(std::optional<int> aMargin) { m_solderMaskMargin = aMargin; }
    
    std::optional<int> getLocalSolderPasteMargin() const { return m_solderPasteMargin; }
    void setLocalSolderPasteMargin(std::optional<int> aMargin) { m_solderPasteMargin = aMargin; }
    
    std::optional<double> getLocalSolderPasteMarginRatio() const { return m_solderPasteMarginRatio; }
    void setLocalSolderPasteMarginRatio(std::optional<double> aRatio) { m_solderPasteMarginRatio = aRatio; }
    
    void setLocalZoneConnection(QtZoneConnection aType) { m_zoneConnection = aType; }
    QtZoneConnection getLocalZoneConnection() const { return m_zoneConnection; }

    // Net tie management
    bool isNetTie() const;
    const QVector<QString>& getNetTiePadGroups() const { return m_netTiePadGroups; }
    void clearNetTiePadGroups() { m_netTiePadGroups.clear(); }
    void addNetTiePadGroup(const QString& aGroup) { m_netTiePadGroups.append(aGroup); }
    QMap<QString, int> mapPadNumbersToNetTieGroups() const;
    QVector<EDA_PAD_DATA*> getNetTiePads(EDA_PAD_DATA* aPad) const;
    void buildNetTieCache();
    const QSet<int>& getNetTieCache(const EDA_BOARD_OBJECT_DATA* aItem) const;

    // Pad operations
    EDA_PAD_DATA* findPadByNumber(const QString& aPadNumber, EDA_PAD_DATA* aSearchAfterMe = nullptr) const;
    EDA_PAD_DATA* getPad(const QPointF& aPosition, QtLayerSet aLayerMask = QtLayerSet::allLayers());
    QVector<const EDA_PAD_DATA*> getPads(const QString& aPadNumber, const EDA_PAD_DATA* aIgnore = nullptr) const;
    unsigned getPadCount(QtIncludeNpthT aIncludeNPTH = QtIncludeNpthT::INCLUDE_NPTH) const;
    unsigned getUniquePadCount(QtIncludeNpthT aIncludeNPTH = QtIncludeNpthT::INCLUDE_NPTH) const;
    QSet<QString> getUniquePadNumbers(QtIncludeNpthT aIncludeNPTH = QtIncludeNpthT::INCLUDE_NPTH) const;
    QString getNextPadNumber(const QString& aLastPadName) const;
    bool hasThroughHolePads() const;

    // Geometry operations
    QRectF getBoundingBox() const override;
    const QRectF getBoundingBox(bool aIncludeText) const;
    const QRectF getLayerBoundingBox(QtLayerSet aLayers) const;
    QPointF getCenter() const override;
    QRectF getFpPadsLocalBbox() const;
    QtShapePolySet getBoundingHull() const;
    bool textOnly() const;

    // Transformation operations
    void move(const QPointF& aMoveVector) override;
    void rotate(const QPointF& aRotCentre, const QtEdaAngle& aAngle);
    void flip(const QPointF& aCentre, QtFlipDirection aFlipDirection) override;
    void moveAnchorPosition(const QPointF& aMoveVector);

    // Hit testing
    bool hitTest(const QPointF& aPosition, int aAccuracy = 0) const;
    bool hitTestAccurate(const QPointF& aPosition, int aAccuracy = 0) const;
    bool hitTest(const QRectF& aRect, bool aContained, int aAccuracy = 0) const;
    bool hitTestOnLayer(const QPointF& aPosition, QtPcbLayerId aLayer, int aAccuracy = 0) const;
    bool hitTestOnLayer(const QRectF& aRect, bool aContained, QtPcbLayerId aLayer, int aAccuracy = 0) const;

    // Field positioning
    void autoPositionFields();
    void incrementReference(int aDelta);

    // Type information
    QString getTypeName() const;
    int getLikelyAttribute() const;
    double getArea(int aPadding = 0) const;

    // Linking
    QUuid getLink() const { return m_link; }
    void setLink(const QUuid& aLink) { m_link = aLink; }

    // File format versioning
    void setFileFormatVersionAtLoad(int aVersion) { m_fileFormatVersionAtLoad = aVersion; }
    int getFileFormatVersionAtLoad() const { return m_fileFormatVersionAtLoad; }

    // Duplication
    EDA_BOARD_OBJECT_DATA* duplicate() const override;
    EDA_BOARD_OBJECT_DATA* duplicateItem(const EDA_BOARD_OBJECT_DATA* aItem, bool aAddToFootprint = false);
    void add3DModel(QtFp3DModel* a3DModel);

    // Validation and checking
    void checkFootprintAttributes(const std::function<void(const QString&)>& aErrorHandler);
    void checkPads(QtUnitsProvider* aUnitsProvider,
                   const std::function<void(const EDA_PAD_DATA*, int, const QString&)>& aErrorHandler);
    void checkShortingPads(const std::function<void(const EDA_PAD_DATA*, const EDA_PAD_DATA*, int,
                                                     const QPointF&)>& aErrorHandler);
    void checkNetTies(const std::function<void(const EDA_BOARD_OBJECT_DATA*, const EDA_BOARD_OBJECT_DATA*,
                                                const EDA_BOARD_OBJECT_DATA*, const QPointF&)>& aErrorHandler);
    void checkNetTiePadGroups(const std::function<void(const QString&)>& aErrorHandler);

    // Library validation
    static bool isLibNameValid(const QString& aName);
    static const QChar* stringLibNameInvalidChars(bool aUserReadable);

    // Update checking
    bool footprintNeedsUpdate(const EDA_FOOTPRINT_DATA* aLibFP, int aCompareFlags = 0,
                              QtReporter* aReporter = nullptr);

    // Comments
    void setInitialComments(QStringList* aInitialComments);
    const QStringList* getInitialComments() const { return m_initialComments; }

    // Coverage calculation
    double coverageRatio(const QtGeneralCollector& aCollector) const;
    static double getCoverageArea(const EDA_BOARD_OBJECT_DATA* aItem, const QtGeneralCollector& aCollector);

    // Courtyard management
    const QtShapePolySet& getCourtyard(QtPcbLayerId aLayer) const;
    const QtShapePolySet& getCachedCourtyard(QtPcbLayerId aLayer) const;
    void buildCourtyardCaches(QtOutlineErrorHandler* aErrorHandler = nullptr);

    // Shape generation
    std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId aLayer = QtPcbLayerId::UndefinedLayer,
                                              int aFlash = 0) const;
    
    void transformPadsToPolySet(QtShapePolySet& aBuffer, QtPcbLayerId aLayer, int aClearance,
                                int aMaxError, QtErrorLoc aErrorLoc,
                                bool aSkipNPTHPadsWihNoCopper = false,
                                bool aSkipPlatedPads = false,
                                bool aSkipNonPlatedPads = false) const;
    
    void transformFPShapesToPolySet(QtShapePolySet& aBuffer, QtPcbLayerId aLayer, int aClearance,
                                    int aError, QtErrorLoc aErrorLoc,
                                    bool aIncludeText = true,
                                    bool aIncludeShapes = true,
                                    bool aIncludePrivateItems = false) const;
    
    void transformFPTextToPolySet(QtShapePolySet& aBuffer, QtPcbLayerId aLayer, int aClearance,
                                  int aError, QtErrorLoc aErrorLoc) const;

    // Text variable support
    void getContextualTextVars(QStringList* aVars) const;
    bool resolveTextVar(QString* token, int aDepth = 0) const;

    // Component class
    void setComponentClass(const QtComponentClass* aClass) { m_componentClass = aClass; }
    const QtComponentClass* getComponentClass() const { return m_componentClass; }
    QString getComponentClassAsString() const;
    void setTransientComponentClassNames(const QSet<QString>& classNames) {
        m_transientComponentClassNames = classNames;
    }
    const QSet<QString>& getTransientComponentClassNames() {
        return m_transientComponentClassNames;
    }
    void clearTransientComponentClassNames() { m_transientComponentClassNames.clear(); }
    void resolveComponentClassNames(EDA_BOARD_DATA* aBoard, const QSet<QString>& aComponentClassNames);

    // Embedded files
    QtEmbeddedFiles* getEmbeddedFiles();
    const QtEmbeddedFiles* getEmbeddedFiles() const;
    QSet<QtOutlineFont*> getFonts() const;
    void embedFonts();

    // Comparison
    double similarity(const EDA_BOARD_OBJECT_DATA& aOther) const override;
    bool operator==(const EDA_BOARD_OBJECT_DATA& aOther) const override;
    bool operator==(const EDA_FOOTPRINT_DATA& aOther) const;

    // Visitor pattern
    void runOnChildren(const std::function<void(EDA_BOARD_OBJECT_DATA*)>& aFunction) const override;
    void runOnDescendants(const std::function<void(EDA_BOARD_OBJECT_DATA*)>& aFunction, int aDepth = 0) const override;


    // Container interface implementation (from EDA_BOARD_OBJECT_CONTAINER)
    void addItem(EDA_BOARD_OBJECT_DATA* item, QtAddMode mode = QtAddMode::Insert, 
                bool skipConnectivity = false) override;
    void removeItem(EDA_BOARD_OBJECT_DATA* item, QtRemoveMode mode = QtRemoveMode::Normal) override;
    int getItemCount() const override;
    bool isEmpty() const override;
    bool contains(const EDA_BOARD_OBJECT_DATA* item) const override;
    QList<EDA_BOARD_OBJECT_DATA*> getAllItems() const override;
    QList<EDA_BOARD_OBJECT_DATA*> getItemsByType(QtKicadType type) const override;
    void clear() override;

    // Descriptive information
    QString getItemDescription(QtUnitsProvider* aUnitsProvider, bool aFull) const override;

    // Cloning
    EDA_BOARD_OBJECT_DATA* clone() const override;



protected:
    virtual void swapData(EDA_BOARD_OBJECT_DATA* aImage) override;

private:
    void addMandatoryFields();

private:
    // Position and orientation
    QPointF m_pos;
    QtEdaAngle m_orient;
    
    // Identity
    QtLibId m_fpid;
    QUuid m_link;
    QtKiidPath m_path;
    
    // Collections
    QVector<EDA_BRD_FIELD_DATA*> m_fields;
    QList<EDA_PAD_DATA*> m_pads;
    QVector<EDA_BOARD_OBJECT_DATA*> m_drawings;
    QVector<EDA_ZONE_DATA*> m_zones;
    QVector<QtPcbGroup*> m_groups;
    QVector<QtFp3DModel> m_3DDrawings;
    
    // Attributes and status
    int m_attributes;
    int m_fpStatus;
    int m_arflag;
    int m_fileFormatVersionAtLoad;
    
    // Design rule overrides
    QtZoneConnection m_zoneConnection;
    std::optional<int> m_clearance;
    std::optional<int> m_solderMaskMargin;
    std::optional<int> m_solderPasteMargin;
    std::optional<double> m_solderPasteMarginRatio;
    
    // Metadata
    QString m_libDescription;
    QString m_keywords;
    QString m_sheetname;
    QString m_sheetfile;
    QString m_filters;
    qint64 m_lastEditTime;
    
    // Net tie information
    QVector<QString> m_netTiePadGroups;
    QMap<const EDA_BOARD_OBJECT_DATA*, QSet<int>> m_netTieCache;
    
    // Private layers
    QtLayerSet m_privateLayers;
    
    // Component class
    const QtComponentClass* m_componentClass;
    QSet<QString> m_transientComponentClassNames;
    
    // Caching
    mutable QRectF m_cachedBoundingBox;
    mutable int m_boundingBoxCacheTimeStamp;
    mutable QRectF m_cachedTextExcludedBBox;
    mutable int m_textExcludedBBoxCacheTimeStamp;
    mutable QtShapePolySet m_cachedHull;
    mutable int m_hullCacheTimeStamp;
    
    mutable QtShapePolySet m_courtyardCacheFront;
    mutable QtShapePolySet m_courtyardCacheBack;
    mutable QtHash128 m_courtyardCacheFrontHash;
    mutable QtHash128 m_courtyardCacheBackHash;
    mutable QMutex m_courtyardCacheMutex;
    
    // Comments
    QStringList* m_initialComments;
    
    // Embedded files support
    bool m_embedFonts;
};

// Comparator structures
struct QtCmpDrawings {
    bool operator()(const EDA_BOARD_OBJECT_DATA* itemA, const EDA_BOARD_OBJECT_DATA* itemB) const;
};

struct QtCmpPads {
    bool operator()(const EDA_PAD_DATA* aFirst, const EDA_PAD_DATA* aSecond) const;
};

struct QtCmpZones {
    bool operator()(const EDA_ZONE_DATA* aFirst, const EDA_ZONE_DATA* aSecond) const;
};

#endif // QT_FOOTPRINT_H、