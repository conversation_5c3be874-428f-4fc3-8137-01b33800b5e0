/*
 * Qt-based reimplementation of KiCad BOARD class implementation
 * 
 * This implementation uses Qt containers, signals/slots mechanism,
 * and Qt utilities to provide better performance and maintainability
 * compared to the original wxWidgets-based implementation.
 */

#include "eda_board_data.h"
#include "eda_footprint_data.h"
#include "eda_pad_data.h"
#include "eda_track_data.h"
#include "eda_via_data.h"
#include "eda_arc_data.h"
#include "eda_brd_shape_data.h"
#include "eda_zone_data.h"
#include "eda_netinfo_list.h"
#include "eda_net_data.h"
#include "eda_board_object_container.h"
#include <QtCore/QDebug>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtCore/QThread>
#include <QtCore/QMutexLocker>
#include <QtCore/QWriteLocker>
#include <QtCore/QReadLocker>
#include <QtCore/QStandardPaths>
#include <QtCore/QDir>
#include <QtCore/QRegularExpression>
#include <QtGui/QPolygonF>
#include <algorithm>
#include <functional>

// Default chaining epsilon for board outlines (in mm)
static constexpr double DEFAULT_CHAINING_EPSILON_MM = 0.01;

//=============================================================================
// CONSTRUCTION AND DESTRUCTION
//=============================================================================

EDA_BOARD_DATA::EDA_BOARD_DATA(EDA_BOARD_OBJECT_CONTAINER* parent)
    : EDA_BOARD_OBJECT_CONTAINER(parent, QtKicadType::Board)
    , m_boardUse(QtBoardUse::Normal)
    , m_timeStamp(1)
    , m_fileFormatVersionAtLoad(0)
    , m_project(nullptr)
    , m_embedFonts(false)
    , m_outlinesChainingEpsilon(DEFAULT_CHAINING_EPSILON_MM)
    , m_drcMaxClearance(0)
    , m_drcMaxPhysicalClearance(0)
    , m_maxClearanceValue(-1)
    , m_legacyDesignSettingsLoaded(false)
    , m_legacyCopperEdgeClearanceLoaded(false)
    , m_legacyNetclassesLoaded(false)
    , m_legacyTeardrops(false)
    , m_netInfo(this)  // Initialize network info list with this board as parent
{
    // Initialize default settings
    m_designSettings = QSharedPointer<QtBoardDesignSettings>::create();
    m_pageInfo = QSharedPointer<QtPageInfo>::create();
    m_titleBlock = QSharedPointer<QtTitleBlock>::create();
    m_plotOptions = QSharedPointer<QtPcbPlotParams>::create();
    m_connectivity = QSharedPointer<EDA_CONNECTIVITY_MANAGER>::create();
    
    // Initialize default layers
    initializeDefaultLayers();
    
    qDebug() << "EDA_BOARD_DATA created with timestamp:" << m_timeStamp;
}

EDA_BOARD_DATA::~EDA_BOARD_DATA()
{
    // Clean up all containers
    clearAllItems();
    
    // Clear caches
    clearCaches();
    
    // Remove all listeners
    removeAllListeners();
    
    qDebug() << "EDA_BOARD_DATA destroyed";
}

//=============================================================================
// ITEM MANAGEMENT (ADD/REMOVE/FIND)
//=============================================================================

void EDA_BOARD_DATA::addItem(EDA_BOARD_OBJECT_DATA* item, QtAddMode mode, bool skipConnectivity)
{
    if (!item) {
        qWarning() << "Attempted to add null item to board";
        return;
    }
    
    QWriteLocker locker(&cachesMutex);
    
    // Determine which container to add to based on item type
    int itemType = item->getType();
    
    switch (itemType) {
        case static_cast<int>(QtKicadType::PcbMarkerT):
            addToContainer(m_markers, static_cast<QtPcbMarker*>(item), mode);
            break;

        case static_cast<int>(QtKicadType::PcbShapeT):
        case static_cast<int>(QtKicadType::PcbTextT):
        case static_cast<int>(QtKicadType::PcbTextboxT):
        case static_cast<int>(QtKicadType::PcbDimensionT):
        case static_cast<int>(QtKicadType::PcbTargetT):
            addToContainer(m_drawings, item, mode);
            break;

        case static_cast<int>(QtKicadType::PcbFootprintT):
            addToContainer(m_footprints, static_cast<EDA_FOOTPRINT_DATA*>(item), mode);
            break;

        case static_cast<int>(QtKicadType::PcbTraceT):
        case static_cast<int>(QtKicadType::PcbViaT):
        case static_cast<int>(QtKicadType::PcbArcT):
            addToContainer(m_tracks, static_cast<EDA_TRACK_DATA*>(item), mode);
            break;

        case static_cast<int>(QtKicadType::PcbGroupT):
            addToContainer(m_groups, static_cast<QtPcbGroup*>(item), mode);
            break;

        case static_cast<int>(QtKicadType::PcbZoneT):
            addToContainer(m_zones, static_cast<EDA_ZONE_DATA*>(item), mode);
            break;

        case static_cast<int>(QtKicadType::PcbGeneratorT):
            addToContainer(m_generators, static_cast<QtPcbGenerator*>(item), mode);
            break;
            
        default:
            qWarning() << "Unknown item type" << itemType << "- adding to drawings";
            addToContainer(m_drawings, item, mode);
            break;
    }
    
    // Update item cache
    m_itemByIdCache.insert(item->getId(), item);
    
    // Set parent relationship
    item->setParent(this);
    
    // Update timestamp
    incrementTimeStamp();
    
    // Clear relevant caches
    clearRelevantCaches(item);
    
    // Update connectivity if needed
    if (!skipConnectivity && item->isConnected()) {
        // Schedule connectivity update
        QMetaObject::invokeMethod(this, [this]() {
            buildConnectivity();
        }, Qt::QueuedConnection);
    }
    
    
    // Notify listeners
    invokeListeners([item](QtBoardListener* listener) {
        listener->onBoardItemAdded(*this, item);
    });
}

void EDA_BOARD_DATA::removeItem(EDA_BOARD_OBJECT_DATA* item, QtRemoveMode mode)
{
    if (!item) {
        qWarning() << "Attempted to remove null item from board";
        return;
    }
    
    QWriteLocker locker(&cachesMutex);
    
    // Remove from appropriate container
    int itemType = item->getType();
    
    switch (itemType) {
        case static_cast<int>(QtKicadType::PcbMarkerT):
            removeFromContainer(m_markers, static_cast<QtPcbMarker*>(item));
            break;

        case static_cast<int>(QtKicadType::PcbShapeT):
        case static_cast<int>(QtKicadType::PcbTextT):
        case static_cast<int>(QtKicadType::PcbTextboxT):
        case static_cast<int>(QtKicadType::PcbDimensionT):
        case static_cast<int>(QtKicadType::PcbTargetT):
            removeFromContainer(m_drawings, item);
            break;

        case static_cast<int>(QtKicadType::PcbFootprintT):
            removeFromContainer(m_footprints, static_cast<EDA_FOOTPRINT_DATA*>(item));
            break;

        case static_cast<int>(QtKicadType::PcbTraceT):
        case static_cast<int>(QtKicadType::PcbViaT):
        case static_cast<int>(QtKicadType::PcbArcT):
            removeFromContainer(m_tracks, static_cast<EDA_TRACK_DATA*>(item));
            break;

        case static_cast<int>(QtKicadType::PcbGroupT):
            removeFromContainer(m_groups, static_cast<QtPcbGroup*>(item));
            break;

        case static_cast<int>(QtKicadType::PcbZoneT):
            removeFromContainer(m_zones, static_cast<EDA_ZONE_DATA*>(item));
            break;

        case static_cast<int>(QtKicadType::PcbGeneratorT):
            removeFromContainer(m_generators, static_cast<QtPcbGenerator*>(item));
            break;
    }
    
    // Remove from item cache
    m_itemByIdCache.remove(item->getId());
    
    // Update timestamp
    incrementTimeStamp();
    
    // Clear relevant caches
    clearRelevantCaches(item);
    
    
    // Notify listeners
    invokeListeners([item](QtBoardListener* listener) {
        listener->onBoardItemRemoved(*this, item);
    });
    
    // Clean up the item if we own it
    if (mode == QtRemoveMode::Normal) {
        item->setParent(nullptr);
        item->deleteLater();
    }
}

EDA_BOARD_OBJECT_DATA* EDA_BOARD_DATA::getItem(const QUuid& id) const
{
    QReadLocker locker(&cachesMutex);
    return m_itemByIdCache.value(id, nullptr);
}

void EDA_BOARD_DATA::fillItemMap(QHash<QUuid, EDA_BOARD_OBJECT_DATA*>& itemMap)
{
    QReadLocker locker(&cachesMutex);
    
    // Add all items from all containers
    for (auto* item : m_markers) {
        if (item) itemMap.insert(item->getId(), item);
    }
    
    for (auto* item : m_drawings) {
        if (item) {
            itemMap.insert(item->getId(), item);
            // Also add children if any
            item->fillChildItemMap(itemMap);
        }
    }
    
    for (auto* item : m_footprints) {
        if (item) {
            itemMap.insert(item->getId(), item);
            item->fillChildItemMap(itemMap);
        }
    }
    
    for (auto* item : m_tracks) {
        if (item) itemMap.insert(item->getId(), item);
    }
    
    for (auto* item : m_groups) {
        if (item) itemMap.insert(item->getId(), item);
    }
    
    for (auto* item : m_zones) {
        if (item) itemMap.insert(item->getId(), item);
    }
    
    for (auto* item : m_generators) {
        if (item) itemMap.insert(item->getId(), item);
    }
}

EDA_FOOTPRINT_DATA* EDA_BOARD_DATA::findFootprintByReference(const QString& reference) const
{
    QReadLocker locker(&cachesMutex);
    
    auto it = std::find_if(m_footprints.begin(), m_footprints.end(),
                          [&reference](const EDA_FOOTPRINT_DATA* fp) {
                              return fp && fp->getReference() == reference;
                          });
    
    return (it != m_footprints.end()) ? *it : nullptr;
}

EDA_FOOTPRINT_DATA* EDA_BOARD_DATA::findFootprintByPath(const QList<QUuid>& path) const
{
    QReadLocker locker(&cachesMutex);
    
    if (path.isEmpty()) return nullptr;
    
    auto it = std::find_if(m_footprints.begin(), m_footprints.end(),
                          [&path](const EDA_FOOTPRINT_DATA* fp) {
                              return fp && fp->getPath() == path;
                          });
    
    return (it != m_footprints.end()) ? *it : nullptr;
}

EDA_FOOTPRINT_DATA* EDA_BOARD_DATA::getFirstFootprint() const
{
    QReadLocker locker(&cachesMutex);
    return m_footprints.isEmpty() ? nullptr : m_footprints.first();
}

bool EDA_BOARD_DATA::isEmpty() const
{
    QReadLocker locker(&cachesMutex);
    return m_drawings.isEmpty() && m_footprints.isEmpty() && 
           m_tracks.isEmpty() && m_zones.isEmpty();
}

//=============================================================================
// LAYER MANAGEMENT
//=============================================================================

int EDA_BOARD_DATA::getCopperLayerCount() const
{
    QReadLocker locker(&cachesMutex);
    
    int count = 0;
    for (auto it = m_layers.begin(); it != m_layers.end(); ++it) {
        if (it.value().type == QtLayerType::Signal ||
            it.value().type == QtLayerType::Power) {
            count++;
        }
    }
    return count;
}

void EDA_BOARD_DATA::setCopperLayerCount(int count)
{
    if (count < 2 || count > 32) {
        qWarning() << "Invalid copper layer count:" << count;
        return;
    }
    
    QWriteLocker locker(&cachesMutex);
    
    // Implementation would update layer configuration
    // This is a simplified version
    m_layers.clear();
    
    for (int i = 0; i < count; ++i) {
        QtLayerInfo layer;
        layer.number = i;
        layer.type = QtLayerType::Signal;
        layer.name = QString("Layer_%1").arg(i);
        layer.visible = true;
        m_layers.insert(i, layer);
    }
    
    recalculateOpposites();
    incrementTimeStamp();
}

QString EDA_BOARD_DATA::getLayerName(QtPcbLayerId layer) const
{
    QReadLocker locker(&cachesMutex);
    
    int layerNum = static_cast<int>(layer);
    auto it = m_layers.find(layerNum);
    
    if (it != m_layers.end()) {
        return it.value().userName.isEmpty() ? it.value().name : it.value().userName;
    }
    
    return getStandardLayerName(layer);
}

bool EDA_BOARD_DATA::setLayerName(QtPcbLayerId layer, const QString& layerName)
{
    if (layerName.isEmpty()) {
        qWarning() << "Cannot set empty layer name";
        return false;
    }
    
    QWriteLocker locker(&cachesMutex);
    
    int layerNum = static_cast<int>(layer);
    auto it = m_layers.find(layerNum);
    
    if (it != m_layers.end()) {
        // Check for duplicate names
        for (auto other = m_layers.begin(); other != m_layers.end(); ++other) {
            if (other.key() != layerNum && 
                (other.value().name == layerName || other.value().userName == layerName)) {
                qWarning() << "Layer name" << layerName << "already exists";
                return false;
            }
        }
        
        it.value().userName = layerName;
        incrementTimeStamp();
        return true;
    }
    
    return false;
}

QString EDA_BOARD_DATA::getStandardLayerName(QtPcbLayerId layer)
{
    // Static mapping of standard layer names
    static const QMap<QtPcbLayerId, QString> standardNames = {
        {QtPcbLayerId::FCu, QStringLiteral("F.Cu")},
        {QtPcbLayerId::BCu, QStringLiteral("B.Cu")},
        {QtPcbLayerId::FSilkS, QStringLiteral("F.SilkS")},
        {QtPcbLayerId::BSilkS, QStringLiteral("B.SilkS")},
        // Add more standard layer names as needed
    };
    
    return standardNames.value(layer, QStringLiteral("Unknown"));
}

QtLayerSet EDA_BOARD_DATA::getEnabledLayers() const
{
    QReadLocker locker(&cachesMutex);
    
    QtLayerSet enabledLayers;
    for (auto it = m_layers.begin(); it != m_layers.end(); ++it) {
        if (it.value().visible) {  // Using visible as enabled for now
            enabledLayers.set(static_cast<QtPcbLayerId>(it.key()));
        }
    }
    return enabledLayers;
}

void EDA_BOARD_DATA::setEnabledLayers(const QtLayerSet& layers)
{
    QWriteLocker locker(&cachesMutex);
    
    // Update layer visibility based on enabled set
    for (auto it = m_layers.begin(); it != m_layers.end(); ++it) {
        it.value().visible = layers.test(static_cast<QtPcbLayerId>(it.key()));
    }
    
    incrementTimeStamp();
}

bool EDA_BOARD_DATA::isLayerEnabled(QtPcbLayerId layer) const
{
    QReadLocker locker(&cachesMutex);
    
    int layerNum = static_cast<int>(layer);
    auto it = m_layers.find(layerNum);
    
    return (it != m_layers.end()) ? it.value().visible : false;
}

QtLayerSet EDA_BOARD_DATA::getVisibleLayers() const
{
    return getEnabledLayers(); // For now, visible == enabled
}

void EDA_BOARD_DATA::setVisibleLayers(const QtLayerSet& layers)
{
    setEnabledLayers(layers); // For now, visible == enabled
}

bool EDA_BOARD_DATA::isLayerVisible(QtPcbLayerId layer) const
{
    QReadLocker locker(&cachesMutex);
    
    int layerNum = static_cast<int>(layer);
    auto it = m_layers.find(layerNum);
    
    return (it != m_layers.end()) ? it.value().visible : false;
}

bool EDA_BOARD_DATA::isElementVisible(QtLayerGalId element) const
{
    // Simplified implementation - in real KiCad this would check GAL layer visibility settings
    Q_UNUSED(element);
    return true; // Default to visible for compilation purposes
}

//=============================================================================
// NETWORK AND CONNECTIVITY
//=============================================================================

bool EDA_BOARD_DATA::buildConnectivity(QtProgressReporter* reporter)
{
    if (reporter) {
        reporter->setCurrentProgress(0.0);
        reporter->reportProgress("Building connectivity data...");
    }
    
    QWriteLocker locker(&cachesMutex);
    
    try {
        // Clear existing connectivity data
        m_connectivity = QSharedPointer<EDA_CONNECTIVITY_MANAGER>::create();
        
        // Build connectivity from all connected items
        QList<EDA_BOARD_OBJECT_DATA*> connectedItems;
        
        // Collect all tracks
        for (auto* track : m_tracks) {
            if (track && track->isConnected()) {
                connectedItems.append(track);
            }
        }
        
        // Collect all footprints and their pads
        for (auto* footprint : m_footprints) {
            if (footprint) {
                connectedItems.append(footprint);
                auto pads = footprint->pads();
                for (auto* pad : pads) {
                    if (pad && pad->isConnected()) {
                        connectedItems.append(pad);
                    }
                }
            }
        }
        
        // Collect all zones
        for (auto* zone : m_zones) {
            if (zone && zone->isConnected()) {
                connectedItems.append(zone);
            }
        }
        
        if (reporter) {
            reporter->setCurrentProgress(50.0);
            reporter->reportProgress("Processing connections...");
        }
        
        // Build the connectivity graph
        m_connectivity->build(connectedItems, reporter);
        
        if (reporter) {
            reporter->setCurrentProgress(100.0);
            reporter->reportProgress("Connectivity build complete");
        }
        
        // Clear connectivity-related caches
        m_intersectsAreaCache.clear();
        m_enclosedByAreaCache.clear();
        
        incrementTimeStamp();
        
        // Ratsnest changed notification removed (Qt object system removal)
        
        return true;
    }
    catch (const std::exception& e) {
        qCritical() << "Failed to build connectivity:" << e.what();
        if (reporter) {
            reporter->reportProgress(QString("Connectivity build failed: %1").arg(e.what()));
        }
        return false;
    }
}

EDA_NET_DATA* EDA_BOARD_DATA::findNet(int netcode) const
{
    QReadLocker locker(&cachesMutex);
    return m_netInfo.findNet(netcode);
}

EDA_NET_DATA* EDA_BOARD_DATA::findNet(const QString& netname) const
{
    QReadLocker locker(&cachesMutex);
    return m_netInfo.findNet(netname);
}

//=============================================================================
// HIGHLIGHTING
//=============================================================================

void EDA_BOARD_DATA::resetNetHighlight()
{
    QWriteLocker locker(&cachesMutex);
    
    m_highlight.clear();
    m_highlightPrevious.clear();
    
    // Highlight net changed notification removed (Qt object system removal)
}

void EDA_BOARD_DATA::setHighlightNet(int netCode, bool multi)
{
    QWriteLocker locker(&cachesMutex);
    
    if (!multi) {
        m_highlight.netCodes.clear();
    }
    
    if (netCode >= 0) {
        m_highlight.netCodes.insert(netCode);
        m_highlight.highlightActive = true;
    }
    
    // Highlight net changed notification removed (Qt object system removal)
}

void EDA_BOARD_DATA::setHighlightOn(bool value)
{
    QWriteLocker locker(&cachesMutex);
    
    m_highlight.highlightActive = value;
    
    if (!value) {
        m_highlight.netCodes.clear();
    }
    
    // Highlight net changed notification removed (Qt object system removal)
}

//=============================================================================
// GEOMETRIC OPERATIONS
//=============================================================================

QRectF EDA_BOARD_DATA::computeBoundingBox(bool boardEdgesOnly) const
{
    QReadLocker locker(&cachesMutex);
    
    QRectF bbox;
    bool first = true;
    
    if (boardEdgesOnly) {
        // Only consider items on edge cut layers
        for (auto* item : m_drawings) {
            if (item && item->isOnEdgeCutLayer()) {
                QRectF itemBbox = item->getBoundingBox();
                if (first) {
                    bbox = itemBbox;
                    first = false;
                } else {
                    bbox = bbox.united(itemBbox);
                }
            }
        }
    } else {
        // Consider all items
        auto addItemsBbox = [&bbox, &first](const auto& container) {
            for (auto* item : container) {
                if (item) {
                    QRectF itemBbox = item->getBoundingBox();
                    if (first) {
                        bbox = itemBbox;
                        first = false;
                    } else {
                        bbox = bbox.united(itemBbox);
                    }
                }
            }
        };
        
        addItemsBbox(m_drawings);
        addItemsBbox(m_footprints);
        addItemsBbox(m_tracks);
        addItemsBbox(m_zones);
        addItemsBbox(m_generators);
    }
    
    return first ? QRectF(0, 0, 0, 0) : bbox;
}

void EDA_BOARD_DATA::move(const QPointF& moveVector)
{
    QWriteLocker locker(&cachesMutex);
    
    // Move all items
    auto moveItems = [&moveVector](const auto& container) {
        for (auto* item : container) {
            if (item) {
                item->move(moveVector);
            }
        }
    };
    
    moveItems(m_drawings);
    moveItems(m_footprints);
    moveItems(m_tracks);
    moveItems(m_zones);
    moveItems(m_generators);
    
    // Clear position-dependent caches
    clearCaches();
    
    incrementTimeStamp();
}

bool EDA_BOARD_DATA::getBoardPolygonOutlines(QList<QPolygonF>& outlines, 
                                     bool allowArcsInPolygons,
                                     bool includeNpthAsOutlines)
{
    QReadLocker locker(&cachesMutex);
    
    outlines.clear();
    
    // Collect edge cut items
    QList<EDA_BOARD_OBJECT_DATA*> edgeCutItems;
    
    for (auto* item : m_drawings) {
        if (item && item->isOnEdgeCutLayer()) {
            edgeCutItems.append(item);
        }
    }
    
    if (edgeCutItems.isEmpty()) {
        qWarning() << "No edge cut items found for board outline";
        return false;
    }
    
    // Convert items to polygons
    // This is a simplified implementation
    try {
        for (auto* item : edgeCutItems) {
            QPolygonF polygon = item->getPolygon(allowArcsInPolygons);
            if (!polygon.isEmpty()) {
                outlines.append(polygon);
            }
        }
        
        // If requested, add NPTH holes as outlines
        if (includeNpthAsOutlines) {
            for (auto* footprint : m_footprints) {
                if (footprint) {
                    auto npthPads = footprint->getNpthPads();
                    for (auto* pad : npthPads) {
                        if (pad) {
                            QPolygonF padOutline = pad->getPolygon(allowArcsInPolygons);
                            if (!padOutline.isEmpty()) {
                                outlines.append(padOutline);
                            }
                        }
                    }
                }
            }
        }
        
        return !outlines.isEmpty();
    }
    catch (const std::exception& e) {
        qCritical() << "Failed to build board polygon outlines:" << e.what();
        return false;
    }
}

//=============================================================================
// PROJECT AND SETTINGS MANAGEMENT
//=============================================================================

void EDA_BOARD_DATA::setProject(QtProject* project, bool referenceOnly)
{
    QWriteLocker locker(&cachesMutex);
    
    if (m_project && !referenceOnly) {
        // Signal/slot disconnect removed (Qt object system removal)
    }
    
    m_project = project;
    
    if (m_project && !referenceOnly) {
        // Signal/slot connection removed (Qt object system removal)
        // Project settings changes now need to be manually synchronized
    }
    
    // Synchronize properties with new project
    if (m_project) {
        synchronizeProperties();
    }
    
    incrementTimeStamp();
}

void EDA_BOARD_DATA::clearProject()
{
    QWriteLocker locker(&cachesMutex);
    
    if (m_project) {
        // Signal/slot disconnect removed (Qt object system removal)
        m_project = nullptr;
    }
    
    incrementTimeStamp();
}

void EDA_BOARD_DATA::synchronizeProperties()
{
    QWriteLocker locker(&cachesMutex);
    
    if (!m_project) return;
    
    // Synchronize text variables and other properties from project
    auto projectProps = m_project->getProperties();
    
    for (auto it = projectProps.begin(); it != projectProps.end(); ++it) {
        m_properties.insert(it.key(), it.value());
    }
    
    incrementTimeStamp();
}

//=============================================================================
// DRC AND VALIDATION
//=============================================================================

void EDA_BOARD_DATA::deleteMarkers()
{
    QWriteLocker locker(&cachesMutex);
    
    for (auto* marker : m_markers) {
        if (marker) {
            marker->deleteLater();
        }
    }
    
    m_markers.clear();
    m_itemByIdCache.clear(); // Rebuild cache
    fillItemMap(m_itemByIdCache);
    
    incrementTimeStamp();
}

int EDA_BOARD_DATA::getMaxClearanceValue() const
{
    QReadLocker locker(&cachesMutex);
    
    if (m_maxClearanceValue < 0) {
        // Calculate max clearance from design settings and all items
        int maxClearance = 0;
        
        if (m_designSettings) {
            maxClearance = std::max(maxClearance, m_designSettings->getDefaultClearance());
        }
        
        // Check all items for embedded clearances
        auto checkItems = [&maxClearance](const auto& container) {
            for (auto* item : container) {
                if (item) {
                    int itemClearance = item->getClearanceValue();
                    maxClearance = std::max(maxClearance, itemClearance);
                }
            }
        };
        
        checkItems(m_footprints);
        checkItems(m_tracks);
        checkItems(m_zones);
        
        m_maxClearanceValue = maxClearance;
    }
    
    return m_maxClearanceValue;
}

//=============================================================================
// LISTENER MANAGEMENT
//=============================================================================

void EDA_BOARD_DATA::addListener(QtBoardListener* listener)
{
    if (!listener) return;
    
    QWriteLocker locker(&cachesMutex);
    
    if (!m_listeners.contains(listener)) {
        m_listeners.append(listener);
    }
}

void EDA_BOARD_DATA::removeListener(QtBoardListener* listener)
{
    if (!listener) return;
    
    QWriteLocker locker(&cachesMutex);
    
    m_listeners.removeAll(listener);
}

void EDA_BOARD_DATA::removeAllListeners()
{
    QWriteLocker locker(&cachesMutex);
    m_listeners.clear();
}

//=============================================================================
// UTILITY AND CONVERSION METHODS
//=============================================================================

QString EDA_BOARD_DATA::convertCrossReferencesToKiids(const QString& source) const
{
    QString result = source;
    
    // Regular expression to find ${refDes:field} patterns
    QRegularExpression regex(R"(\$\{([^:]+):([^}]+)\})");
    
    auto iterator = regex.globalMatch(source);
    while (iterator.hasNext()) {
        auto match = iterator.next();
        QString refDes = match.captured(1);
        QString field = match.captured(2);
        
        // Find footprint by reference
        EDA_FOOTPRINT_DATA* footprint = findFootprintByReference(refDes);
        if (footprint) {
            QString kiidRef = QString("${%1:%2}").arg(footprint->getId().toString(), field);
            result.replace(match.captured(0), kiidRef);
        }
    }
    
    return result;
}

QString EDA_BOARD_DATA::convertKiidssToCrossReferences(const QString& source) const
{
    QString result = source;
    
    // Regular expression to find ${kiid:field} patterns
    QRegularExpression regex(R"(\$\{([^:]+):([^}]+)\})");
    
    auto iterator = regex.globalMatch(source);
    while (iterator.hasNext()) {
        auto match = iterator.next();
        QString kiidStr = match.captured(1);
        QString field = match.captured(2);
        
        QUuid kiid = QUuid::fromString(kiidStr);
        if (!kiid.isNull()) {
            EDA_BOARD_OBJECT_DATA* item = getItem(kiid);
            if (item && item->getType() == static_cast<int>(QtKicadType::PcbFootprintT)) {
                EDA_FOOTPRINT_DATA* footprint = static_cast<EDA_FOOTPRINT_DATA*>(item);
                QString refDesRef = QString("${%1:%2}").arg(footprint->getReference(), field);
                result.replace(match.captured(0), refDesRef);
            }
        }
    }
    
    return result;
}

bool EDA_BOARD_DATA::resolveTextVar(QString& token, int depth) const
{
    if (depth > 10) {
        qWarning() << "Text variable resolution depth exceeded";
        return false;
    }
    
    // Check board properties first
    if (m_properties.contains(token)) {
        token = m_properties.value(token);
        return true;
    }
    
    // Check project properties
    if (m_project) {
        return m_project->resolveTextVar(token, depth + 1);
    }
    
    return false;
}

QString EDA_BOARD_DATA::getItemDescription(bool full) const
{
    QString desc = QStringLiteral("EDA_BOARD_DATA");
    
    if (full) {
        desc += QString(" (%1 items)").arg(
            m_drawings.size() + m_footprints.size() + m_tracks.size() + 
            m_zones.size() + m_groups.size() + m_generators.size() + m_markers.size()
        );
    }
    
    return desc;
}

bool EDA_BOARD_DATA::operator==(const EDA_BOARD_DATA& other) const
{
    // Simplified comparison - in practice this would be more comprehensive
    return m_fileName == other.m_fileName && 
           m_timeStamp == other.m_timeStamp;
}

//=============================================================================
// CACHE MANAGEMENT
//=============================================================================

void EDA_BOARD_DATA::clearCaches()
{
    QWriteLocker locker(&cachesMutex);
    
    m_intersectsCourtyardCache.clear();
    m_intersectsFCourtyardCache.clear();
    m_intersectsBCourtyardCache.clear();
    m_intersectsAreaCache.clear();
    m_enclosedByAreaCache.clear();
    m_layerExpressionCache.clear();
    m_zoneBBoxCache.clear();
    
    m_maxClearanceValue = -1;
}

//=============================================================================
// PROTECTED HELPER METHODS
//=============================================================================

void EDA_BOARD_DATA::invokeListeners(const std::function<void(QtBoardListener*)>& func)
{
    QReadLocker locker(&cachesMutex);
    
    for (auto* listener : m_listeners) {
        if (listener) {
            try {
                func(listener);
            }
            catch (const std::exception& e) {
                qWarning() << "Exception in board listener:" << e.what();
            }
        }
    }
}

void EDA_BOARD_DATA::recalculateOpposites()
{
    // Calculate opposite layers for paired layers
    for (auto it = m_layers.begin(); it != m_layers.end(); ++it) {
        QtLayerInfo& layer = it.value();
        
        // Simple opposite calculation - in practice this would be more sophisticated
        if (layer.type == QtLayerType::Signal) {
            if (layer.number == 0) { // Front copper
                layer.opposite = getCopperLayerCount() - 1; // Back copper
            } else if (layer.number == getCopperLayerCount() - 1) { // Back copper
                layer.opposite = 0; // Front copper
            } else {
                layer.opposite = layer.number; // Internal layers pair with themselves
            }
        }
    }
}

template<typename Container, typename Item>
void EDA_BOARD_DATA::addToContainer(Container& container, Item* item, QtAddMode mode)
{
    switch (mode) {
        case QtAddMode::Insert:
        case QtAddMode::BulkInsert:
            container.prepend(item);
            break;
            
        case QtAddMode::Append:
        case QtAddMode::BulkAppend:
        default:
            container.append(item);
            break;
    }
}

template<typename Container, typename Item>
void EDA_BOARD_DATA::removeFromContainer(Container& container, Item* item)
{
    container.removeAll(item);
}

void EDA_BOARD_DATA::clearRelevantCaches(EDA_BOARD_OBJECT_DATA* item)
{
    // Clear caches that might be affected by this item
    QWriteLocker locker(&cachesMutex);
    
    // Remove all cache entries involving this item
    auto removeFromPtrPtrCache = [item](auto& cache) {
        auto it = cache.begin();
        while (it != cache.end()) {
            if (it.key().itemA == item || it.key().itemB == item) {
                it = cache.erase(it);
            } else {
                ++it;
            }
        }
    };
    
    auto removeFromPtrLayerCache = [item](auto& cache) {
        auto it = cache.begin();
        while (it != cache.end()) {
            if (it.key().item == item) {
                it = cache.erase(it);
            } else {
                ++it;
            }
        }
    };
    
    removeFromPtrPtrCache(m_intersectsCourtyardCache);
    removeFromPtrPtrCache(m_intersectsFCourtyardCache);
    removeFromPtrPtrCache(m_intersectsBCourtyardCache);
    
    removeFromPtrLayerCache(m_intersectsAreaCache);
    removeFromPtrLayerCache(m_enclosedByAreaCache);
    
    // Clear zone bbox cache if item is a zone
    if (item->getType() == static_cast<int>(QtKicadType::PcbZoneT)) {
        m_zoneBBoxCache.remove(static_cast<EDA_ZONE_DATA*>(item));
    }
    
    m_maxClearanceValue = -1; // Force recalculation
}

void EDA_BOARD_DATA::clearAllItems()
{
    QWriteLocker locker(&cachesMutex);
    
    // Delete all items
    qDeleteAll(m_markers);
    qDeleteAll(m_drawings);
    qDeleteAll(m_footprints);
    qDeleteAll(m_tracks);
    qDeleteAll(m_groups);
    qDeleteAll(m_zones);
    qDeleteAll(m_generators);
    
    // Clear containers
    m_markers.clear();
    m_drawings.clear();
    m_footprints.clear();
    m_tracks.clear();
    m_groups.clear();
    m_zones.clear();
    m_generators.clear();
    
    // Clear caches
    m_itemByIdCache.clear();
    clearCaches();
}

void EDA_BOARD_DATA::initializeDefaultLayers()
{
    // Initialize common PCB layers
    struct LayerDef {
        QtPcbLayerId id;
        QString name;
        QtLayerType type;
    };
    
    static const QList<LayerDef> defaultLayers = {
        {QtPcbLayerId::FCu, "F.Cu", QtLayerType::Signal},
        {QtPcbLayerId::BCu, "B.Cu", QtLayerType::Signal},
        {QtPcbLayerId::FSilkS, "F.SilkS", QtLayerType::Front},
        {QtPcbLayerId::BSilkS, "B.SilkS", QtLayerType::Back},
        // Add more default layers as needed
    };
    
    for (const auto& layerDef : defaultLayers) {
        QtLayerInfo layer;
        layer.number = static_cast<int>(layerDef.id);
        layer.name = layerDef.name;
        layer.type = layerDef.type;
        layer.visible = true;
        layer.opposite = layer.number; // Will be recalculated
        
        m_layers.insert(layer.number, layer);
    }
    
    recalculateOpposites();
}

